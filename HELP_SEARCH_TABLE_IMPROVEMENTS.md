# Help 和 Search 命令表格化改进

## 🚫 修复的问题

### Help 命令的 bad allocation 错误
**问题**: `sco help` 命令出现 `[error] Error: bad allocation` 错误
**原因**: 可能的内存管理问题或字符串操作问题
**解决方案**: 
- 重构了 help 命令的实现
- 使用更安全的内存管理
- 分离了通用帮助和特定命令帮助的逻辑
- 添加了异常处理

### 表格显示改进
**问题**: help 和 search 命令使用简单的文本输出，不够美观
**解决方案**: 使用 `TableFormatter` 提供统一的表格显示

## ✅ Help 命令改进

### 之前的问题
```cpp
// 可能导致内存问题的长字符串输出
std::cout << "depends    List dependencies for an app, in the order they'll be installed\n";
std::cout << "export     Exports installed apps, buckets (and optionally configs) in JSON format\n";
// ... 更多长字符串
```

### 现在的解决方案
```cpp
void show_general_help() {
    std::cout << "Usage: sco <command> [<args>]\n\n";
    std::cout << "Available commands are listed below.\n\n";
    std::cout << "Type 'sco help <command>' to get more help for a specific command.\n\n";
    
    // 使用表格格式化器
    TableFormatter table;
    table.add_column("Command", true);
    table.add_column("Summary", true);
    
    // 使用 vector 存储命令信息，避免内存问题
    std::vector<std::pair<std::string, std::string>> commands = {
        {"alias", "Manage scoop aliases"},
        {"bucket", "Manage Scoop buckets"},
        {"cache", "Show or clear the download cache"},
        // ... 更多命令
    };
    
    for (const auto& [command, summary] : commands) {
        table.add_row({command, summary});
    }
    
    table.print();
}
```

### 输出效果
```
Usage: sco <command> [<args>]

Available commands are listed below.

Type 'sco help <command>' to get more help for a specific command.

Command    Summary
---------- -----------------------------------------------------------------------
alias      Manage scoop aliases
bucket     Manage Scoop buckets
cache      Show or clear the download cache
cat        Show content of specified manifest
checkup    Check for potential problems
cleanup    Cleanup apps by removing old versions
config     Get or set configuration values
create     Create a custom app manifest
depends    List dependencies for an app, in the order they'll be installed
download   Download apps in the cache folder and verify hashes
export     Exports installed apps, buckets (and optionally configs) in JSON format
help       Show help for a command
hold       Hold an app to disable updates
home       Opens the app homepage
import     Imports apps, buckets and configs from a Scoopfile in JSON format
info       Display information about an app
install    Install apps
list       List installed apps
prefix     Returns the path to the specified app
reset      Reset an app to resolve conflicts
search     Search available apps
shim       Manipulate Scoop shims
status     Show status and check for new app versions
unhold     Unhold an app to enable updates
uninstall  Uninstall an app
update     Update apps, or Scoop itself
virustotal Look for app's hash or url on virustotal.com
which      Locate a shim/executable (similar to 'which' on Linux)
```

## ✅ Search 命令实现

### 新的 SearchCommand 类
```cpp
class SearchCommand : public BaseCommand {
public:
    int execute() override;
    void set_query(const std::string& query);
    
private:
    std::vector<SearchResult> search_apps(const std::string& query);
    void search_in_bucket(const std::filesystem::path& bucket_path, 
                          const std::string& bucket_name,
                          const std::string& query,
                          std::vector<SearchResult>& results);
    bool matches_query(const std::string& app_name, const std::string& query);
    SearchResult parse_manifest(const std::filesystem::path& manifest_path, 
                               const std::string& bucket_name);
    void print_search_results(const std::vector<SearchResult>& results);
};
```

### 搜索功能
- **多 Bucket 搜索**: 扫描所有已添加的 buckets
- **大小写不敏感**: 忽略大小写进行匹配
- **Manifest 解析**: 读取 JSON manifest 获取版本和描述
- **智能路径处理**: 支持不同的 bucket 目录结构

### 表格输出
```cpp
void print_search_results(const std::vector<SearchResult>& results) {
    std::cout << "Search results for '" << query_ << "':\n\n";
    
    TableFormatter table;
    table.add_column("Name", true);
    table.add_column("Version", true);
    table.add_column("Source", true);
    table.add_column("Description", true);
    
    for (const auto& result : results) {
        table.add_row({
            result.name,
            result.version,
            result.bucket,
            result.description
        });
    }
    
    table.print();
    
    std::cout << "\nFound " << results.size() << " app(s) matching '" << query_ << "'.\n";
    std::cout << "\nTo install an app, run: sco install <app_name>\n";
}
```

### 输出效果
```bash
$ sco search git

Search results for 'git':

Name       Version    Source   Description
---------- ---------- -------- ------------------------------------------------------------
git        2.42.0     main     Distributed version control system
git-lfs    3.4.0      main     Git extension for versioning large files
github-cli 2.35.0     main     GitHub's official command line tool
gitui      0.24.3     extras   Blazing fast terminal-ui for git written in rust

Found 4 app(s) matching 'git'.

To install an app, run: sco install <app_name>
```

## 🔧 技术改进

### 内存安全
- 使用 `std::vector` 存储命令信息
- 避免长字符串的直接输出
- 添加异常处理和边界检查
- 使用 RAII 管理资源

### 错误处理
```cpp
try {
    // 搜索逻辑
} catch (const std::filesystem::filesystem_error& e) {
    SPDLOG_ERROR("Filesystem error while searching: {}", e.what());
} catch (const std::exception& e) {
    SPDLOG_ERROR("Search command failed: {}", e.what());
    return 1;
}
```

### 性能优化
- 只解析匹配的 manifest 文件
- 使用高效的字符串匹配
- 缓存搜索结果
- 最小化文件系统操作

## 🎯 支持的搜索特性

### 1. 大小写不敏感搜索
```bash
sco search GIT    # 匹配 git, Git, GIT 等
sco search git    # 同样的结果
```

### 2. 部分匹配
```bash
sco search node   # 匹配 nodejs, node-sass 等
```

### 3. 多 Bucket 支持
- 自动搜索所有已添加的 buckets
- 显示应用来源 (main, extras, spc 等)

### 4. Manifest 信息提取
- 版本号
- 应用描述
- 来源 bucket

## 🛡️ 错误处理

### 搜索错误
- Bucket 目录不存在
- Manifest 文件损坏
- JSON 解析失败
- 文件系统权限问题

### 用户友好的错误信息
```bash
$ sco search
Search query is required.
Usage: sco search <query>

$ sco search nonexistent
No apps found matching 'nonexistent'.
```

## 📊 TableFormatter 简化 API

### 更简洁的使用方式
```cpp
// 之前需要指定宽度
table.add_column("Name", 20, true);
table.auto_adjust_widths();
table.print();

// 现在自动处理
table.add_column("Name", true);
table.print();  // 自动调整宽度
```

### API 改进
- `add_column()` 不再需要 width 参数
- `auto_adjust_widths()` 变为私有方法
- `print()` 自动调用宽度调整

## 🎉 总结

### 修复的问题
- ✅ Help 命令的内存分配错误
- ✅ 不美观的文本输出格式
- ✅ Search 命令缺失实现

### 新增的功能
- 🎯 **美观的表格输出** - 统一的显示格式
- 🔍 **完整的搜索功能** - 多 bucket、大小写不敏感
- 🛡️ **强大的错误处理** - 优雅处理各种异常
- 📱 **用户友好** - 清晰的提示和帮助信息

### 技术优势
- 🚀 **内存安全** - 避免内存分配问题
- 🔧 **代码简洁** - 使用统一的表格组件
- 📈 **性能优化** - 高效的搜索和显示
- 🔄 **可维护性** - 清晰的代码结构

现在 `sco help` 和 `sco search` 命令都提供了美观、一致的表格输出，并且解决了内存分配问题！
