#pragma once

#include <memory>
#include <unordered_map>
#include <functional>
#include <sstream>
#include <CLI/CLI.hpp>
#include <spdlog/spdlog.h>

#include "base_command.hpp"
#include "help_command.hpp"
#include "list_command.hpp"
#include "config_command.hpp"
#include "checkup_command.hpp"
#include "status_command.hpp"
#include "cache_command.hpp"
#include "search_command.hpp"

namespace sco {

// Helper function to join strings
template<typename Container>
std::string join_strings(const Container& container, const std::string& delimiter) {
    if (container.empty()) return "";

    std::ostringstream oss;
    auto it = container.begin();
    oss << *it;
    ++it;

    for (; it != container.end(); ++it) {
        oss << delimiter << *it;
    }

    return oss.str();
}

class CommandManager {
public:
    CommandManager() {
        // Commands will be registered when register_commands is called
    }
    
    ~CommandManager() = default;
    
    void register_commands(CLI::App& app) {
        register_help_command(app);
        register_list_command(app);
        register_install_command(app);
        register_uninstall_command(app);
        register_update_command(app);
        register_search_command(app);
        register_info_command(app);
        register_home_command(app);
        register_bucket_command(app);
        register_config_command(app);
        register_cache_command(app);
        register_cat_command(app);
        register_checkup_command(app);
        register_cleanup_command(app);
        register_create_command(app);
        register_depends_command(app);
        register_download_command(app);
        register_export_command(app);
        register_import_command(app);
        register_hold_command(app);
        register_unhold_command(app);
        register_prefix_command(app);
        register_reset_command(app);
        register_shim_command(app);
        register_status_command(app);
        register_virustotal_command(app);
        register_which_command(app);
        register_alias_command(app);
    }
    
    int execute() {
        if (current_command_) {
            return current_command_();
        }
        return 0;
    }
    
private:
    std::unordered_map<std::string, std::unique_ptr<BaseCommand>> commands_;
    std::function<int()> current_command_;
    
    void register_command(const std::string& name, std::unique_ptr<BaseCommand> command) {
        commands_[name] = std::move(command);
    }
    
    void register_help_command(CLI::App& app) {
        auto help_cmd = std::make_unique<HelpCommand>();
        help_cmd->set_app(&app);
        
        auto* sub = app.add_subcommand("help", "Show help for a command");
        std::string command_name;
        sub->add_option("command", command_name, "Command to show help for");
        
        sub->callback([this, help_cmd_ptr = help_cmd.get(), &command_name]() {
            help_cmd_ptr->set_command_name(command_name);
            current_command_ = [help_cmd_ptr]() { return help_cmd_ptr->execute(); };
        });
        
        register_command("help", std::move(help_cmd));
    }
    
    void register_list_command(CLI::App& app) {
        auto list_cmd = std::make_unique<ListCommand>();
        
        auto* sub = app.add_subcommand("list", "List installed apps");
        
        sub->callback([this, list_cmd_ptr = list_cmd.get()]() {
            current_command_ = [list_cmd_ptr]() { return list_cmd_ptr->execute(); };
        });
        
        register_command("list", std::move(list_cmd));
    }
    
    // Placeholder implementations for other commands
    void register_install_command(CLI::App& app) {
        auto* sub = app.add_subcommand("install", "Install apps");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to install")->required();
        
        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                SPDLOG_INFO("Install command called with {} apps", apps.size());
                SPDLOG_INFO("Install command not yet implemented.");
                SPDLOG_INFO("Apps to install: {}", join_strings(apps, ", "));
                return 0;
            };
        });
    }
    
    void register_uninstall_command(CLI::App& app) {
        auto* sub = app.add_subcommand("uninstall", "Uninstall an app");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to uninstall")->required();
        
        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                SPDLOG_INFO("Uninstall command called with {} apps", apps.size());
                SPDLOG_INFO("Uninstall command not yet implemented.");
                SPDLOG_INFO("Apps to uninstall: {}", join_strings(apps, ", "));
                return 0;
            };
        });
    }
    
    void register_update_command(CLI::App& app) {
        auto* sub = app.add_subcommand("update", "Update apps, or Scoop itself");
        std::vector<std::string> apps;
        bool all = false;
        sub->add_option("apps", apps, "Apps to update");
        sub->add_flag("--all,-a", all, "Update all apps");
        
        sub->callback([this, apps, all]() {
            current_command_ = [apps, all]() {
                if (all) {
                    SPDLOG_INFO("Update all apps");
                    SPDLOG_INFO("Update all apps - not yet implemented.");
                } else if (!apps.empty()) {
                    SPDLOG_INFO("Update specific apps");
                    SPDLOG_INFO("Update specific apps - not yet implemented.");
                    SPDLOG_INFO("Apps to update: {}", join_strings(apps, ", "));
                } else {
                    SPDLOG_INFO("Update Scoop itself");
                    SPDLOG_INFO("Update Scoop itself - not yet implemented.");
                }
                return 0;
            };
        });
    }
    
    void register_search_command(CLI::App& app) {
        auto search_cmd = std::make_unique<SearchCommand>();

        auto* sub = app.add_subcommand("search", "Search available apps");
        std::string query;
        sub->add_option("query", query, "Search query")->required();

        sub->callback([this, search_cmd_ptr = search_cmd.get(), &query]() {
            search_cmd_ptr->set_query(query);
            current_command_ = [search_cmd_ptr]() { return search_cmd_ptr->execute(); };
        });

        register_command("search", std::move(search_cmd));
    }
    
    void register_info_command(CLI::App& app) {
        auto* sub = app.add_subcommand("info", "Display information about an app");
        std::string app_name;
        sub->add_option("app", app_name, "App name")->required();
        
        sub->callback([this, app_name]() {
            current_command_ = [app_name]() {
                spdlog::info("Info for app: {}", app_name);
                std::cout << "Info command not yet implemented.\n";
                std::cout << "App: " << app_name << "\n";
                return 0;
            };
        });
    }
    
    void register_home_command(CLI::App& app) {
        auto* sub = app.add_subcommand("home", "Opens the app homepage");
        std::string app_name;
        sub->add_option("app", app_name, "App name")->required();

        sub->callback([this, app_name]() {
            current_command_ = [app_name]() {
                spdlog::info("Open homepage for app: {}", app_name);
                std::cout << "Home command not yet implemented.\n";
                std::cout << "App: " << app_name << "\n";
                return 0;
            };
        });
    }

    // Placeholder implementations for remaining commands
    void register_bucket_command(CLI::App& app) {
        auto* sub = app.add_subcommand("bucket", "Manage Scoop buckets");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Bucket command not yet implemented.\n";
                return 0;
            };
        });
    }

    void register_config_command(CLI::App& app) {
        auto config_cmd = std::make_unique<ConfigCommand>();

        auto* sub = app.add_subcommand("config", "Get or set configuration values");
        std::string key, value;
        sub->add_option("key", key, "Configuration key");
        sub->add_option("value", value, "Configuration value");

        sub->callback([this, config_cmd_ptr = config_cmd.get(), &key, &value]() {
            config_cmd_ptr->set_key(key);
            config_cmd_ptr->set_value(value);
            current_command_ = [config_cmd_ptr]() { return config_cmd_ptr->execute(); };
        });

        register_command("config", std::move(config_cmd));
    }

    void register_cache_command(CLI::App& app) {
        auto cache_cmd = std::make_unique<CacheCommand>();

        auto* sub = app.add_subcommand("cache", "Show or clear the download cache");
        std::string action, app_name;
        sub->add_option("action", action, "Action: show, rm");
        sub->add_option("app", app_name, "App name (for rm action)");

        sub->callback([this, cache_cmd_ptr = cache_cmd.get(), &action, &app_name]() {
            cache_cmd_ptr->set_action(action);
            cache_cmd_ptr->set_app_name(app_name);
            current_command_ = [cache_cmd_ptr]() { return cache_cmd_ptr->execute(); };
        });

        register_command("cache", std::move(cache_cmd));
    }

    void register_cat_command(CLI::App& app) {
        auto* sub = app.add_subcommand("cat", "Show content of specified manifest");
        std::string app_name;
        sub->add_option("app", app_name, "App name")->required();

        sub->callback([this, app_name]() {
            current_command_ = [app_name]() {
                std::cout << "Cat command not yet implemented.\n";
                std::cout << "App: " << app_name << "\n";
                return 0;
            };
        });
    }

    void register_checkup_command(CLI::App& app) {
        auto checkup_cmd = std::make_unique<CheckupCommand>();

        auto* sub = app.add_subcommand("checkup", "Check for potential problems");

        sub->callback([this, checkup_cmd_ptr = checkup_cmd.get()]() {
            current_command_ = [checkup_cmd_ptr]() { return checkup_cmd_ptr->execute(); };
        });

        register_command("checkup", std::move(checkup_cmd));
    }

    void register_cleanup_command(CLI::App& app) {
        auto* sub = app.add_subcommand("cleanup", "Cleanup apps by removing old versions");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Cleanup command not yet implemented.\n";
                return 0;
            };
        });
    }

    void register_create_command(CLI::App& app) {
        auto* sub = app.add_subcommand("create", "Create a custom app manifest");
        std::string app_name;
        sub->add_option("app", app_name, "App name")->required();

        sub->callback([this, app_name]() {
            current_command_ = [app_name]() {
                std::cout << "Create command not yet implemented.\n";
                std::cout << "App: " << app_name << "\n";
                return 0;
            };
        });
    }

    void register_depends_command(CLI::App& app) {
        auto* sub = app.add_subcommand("depends", "List dependencies for an app");
        std::string app_name;
        sub->add_option("app", app_name, "App name")->required();

        sub->callback([this, app_name]() {
            current_command_ = [app_name]() {
                std::cout << "Depends command not yet implemented.\n";
                std::cout << "App: " << app_name << "\n";
                return 0;
            };
        });
    }

    void register_download_command(CLI::App& app) {
        auto* sub = app.add_subcommand("download", "Download apps in the cache folder and verify hashes");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to download")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Download command not yet implemented.\n";
                std::cout << "Apps to download: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_export_command(CLI::App& app) {
        auto* sub = app.add_subcommand("export", "Exports installed apps, buckets (and optionally configs) in JSON format");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Export command not yet implemented.\n";
                return 0;
            };
        });
    }

    void register_import_command(CLI::App& app) {
        auto* sub = app.add_subcommand("import", "Imports apps, buckets and configs from a Scoopfile in JSON format");
        std::string file;
        sub->add_option("file", file, "JSON file to import")->required();

        sub->callback([this, file]() {
            current_command_ = [file]() {
                std::cout << "Import command not yet implemented.\n";
                std::cout << "File: " << file << "\n";
                return 0;
            };
        });
    }

    void register_hold_command(CLI::App& app) {
        auto* sub = app.add_subcommand("hold", "Hold an app to disable updates");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to hold")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Hold command not yet implemented.\n";
                std::cout << "Apps to hold: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_unhold_command(CLI::App& app) {
        auto* sub = app.add_subcommand("unhold", "Unhold an app to enable updates");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to unhold")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Unhold command not yet implemented.\n";
                std::cout << "Apps to unhold: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_prefix_command(CLI::App& app) {
        auto* sub = app.add_subcommand("prefix", "Returns the path to the specified app");
        std::string app_name;
        sub->add_option("app", app_name, "App name")->required();

        sub->callback([this, app_name]() {
            current_command_ = [app_name]() {
                std::cout << "Prefix command not yet implemented.\n";
                std::cout << "App: " << app_name << "\n";
                return 0;
            };
        });
    }

    void register_reset_command(CLI::App& app) {
        auto* sub = app.add_subcommand("reset", "Reset an app to resolve conflicts");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to reset")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "Reset command not yet implemented.\n";
                std::cout << "Apps to reset: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_shim_command(CLI::App& app) {
        auto* sub = app.add_subcommand("shim", "Manipulate Scoop shims");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Shim command not yet implemented.\n";
                return 0;
            };
        });
    }

    void register_status_command(CLI::App& app) {
        auto status_cmd = std::make_unique<StatusCommand>();

        auto* sub = app.add_subcommand("status", "Show status and check for new app versions");

        sub->callback([this, status_cmd_ptr = status_cmd.get()]() {
            current_command_ = [status_cmd_ptr]() { return status_cmd_ptr->execute(); };
        });

        register_command("status", std::move(status_cmd));
    }

    void register_virustotal_command(CLI::App& app) {
        auto* sub = app.add_subcommand("virustotal", "Look for app's hash or url on virustotal.com");
        std::vector<std::string> apps;
        sub->add_option("apps", apps, "Apps to check")->required();

        sub->callback([this, apps]() {
            current_command_ = [apps]() {
                std::cout << "VirusTotal command not yet implemented.\n";
                std::cout << "Apps to check: ";
                for (const auto& app : apps) {
                    std::cout << app << " ";
                }
                std::cout << "\n";
                return 0;
            };
        });
    }

    void register_which_command(CLI::App& app) {
        auto* sub = app.add_subcommand("which", "Locate a shim/executable (similar to 'which' on Linux)");
        std::string command;
        sub->add_option("command", command, "Command to locate")->required();

        sub->callback([this, command]() {
            current_command_ = [command]() {
                std::cout << "Which command not yet implemented.\n";
                std::cout << "Command: " << command << "\n";
                return 0;
            };
        });
    }

    void register_alias_command(CLI::App& app) {
        auto* sub = app.add_subcommand("alias", "Manage scoop aliases");
        sub->callback([this]() {
            current_command_ = []() {
                std::cout << "Alias command not yet implemented.\n";
                return 0;
            };
        });
    }
};

} // namespace sco
